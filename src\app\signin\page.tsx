// 📄 src/app/signin/page.tsx
// Pagina di login con Supabase Auth UI, stile card Tail<PERSON>, redirect automatico su successo.
// Usa env NEXT_PUBLIC_SUPABASE_URL/KEY tramite supabaseClient.ts

"use client";
// import { useRouter } from "next/navigation";
import { Auth } from "@supabase/auth-ui-react";
import { ThemeSupa } from "@supabase/auth-ui-shared";
import { supabase } from "@/lib/supabaseClient";

export default function SignInPage() {
  // const router = useRouter();
  return (
    <main className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <div className="rounded-xl shadow p-6 w-full max-w-md bg-white">
        <Auth
          supabaseClient={supabase}
          appearance={{ theme: ThemeSupa }}
          theme="default"
          providers={[]}
          socialLayout="horizontal"
          redirectTo={typeof window !== "undefined" ? `${window.location.origin}/` : undefined}
          magicLink
          onlyThirdPartyProviders={false}
          localization={{ variables: { sign_in: { email_label: "Email", password_label: "Password" } } }}
          view="sign_in"
          /*
          // onAuthStateChange is not supported by Auth component. If you need to handle sign-in, use a useEffect with supabase.auth.onAuthStateChange in a parent/client component.
          */
        />
      </div>
    </main>
  );
}
