"use client";

import { useRouter } from "next/navigation";
import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import TenantSwitch from "./TenantSwitch";
import SearchBar from "./SearchBar";

/**
 * Navbar principale dell'applicazione
 * Include logo, tenant switch, bottone inserisci annuncio e search bar
 */
export default function Navbar() {
  const router = useRouter();

  return (
    <nav className="border-b bg-white">
      {/* Barra principale */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <button
              onClick={() => router.push("/")}
              className="text-xl font-bold text-primary hover:text-primary/80 transition-colors"
            >
              ReBuildLink
            </button>
          </div>

          {/* Desktop: TenantSwitch + Inserisci Annuncio */}
          <div className="hidden lg:flex items-center space-x-4">
            <TenantSwitch />
            <Button 
              variant="default" 
              className="bg-primary text-white hover:bg-primary/90"
              onClick={() => router.push("/inserisci-annuncio")}
            >
              Inserisci annuncio
            </Button>
          </div>

          {/* Mobile: Hamburger Menu */}
          <div className="lg:hidden">
            <Button variant="ghost" size="sm">
              <Menu className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Search Bar - sotto la barra principale */}
        <div className="pb-4">
          <SearchBar />
        </div>
      </div>
    </nav>
  );
}
