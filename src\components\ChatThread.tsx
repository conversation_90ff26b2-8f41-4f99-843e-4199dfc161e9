"use client";

import { useEffect, useRef, useState } from "react";
import useS<PERSON> from "swr";
import { supabase } from "@/lib/supabaseClient";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";

type Message = { 
  id: number; 
  announcement_id: number; 
  user_id: string; 
  content: string; 
  created_at: string 
};

const fetcher = (url: string) => fetch(url).then(res => res.json());

/**
 * Componente chat realtime per annunci
 * Utilizza Supabase Realtime per messaggi in tempo reale
 */
export default function ChatThread({ announcementId }: { announcementId: number }) {
  const bottomRef = useRef<HTMLDivElement>(null);
  const { data: initial, mutate } = useSWR<Message[]>(`/api/announcements/${announcementId}/messages`, fetcher);
  const [messages, setMessages] = useState<Message[]>(() => initial ?? []);
  const [text, setText] = useState("");
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  // Ottieni l'ID utente corrente
  useEffect(() => {
    const getUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setCurrentUserId(session?.user?.id || null);
    };
    getUser();
  }, []);

  // Aggiorna messages quando arrivano i dati iniziali
  useEffect(() => {
    if (initial) {
      setMessages(initial);
    }
  }, [initial]);

  // Realtime subscription
  useEffect(() => {
    if (!announcementId) return;
    
    const channel = supabase
      .channel(`messages:announcement:${announcementId}`)
      .on(
        "postgres_changes",
        { 
          event: "INSERT", 
          schema: "public", 
          table: "messages", 
          filter: `announcement_id=eq.${announcementId}` 
        },
        (payload) => {
          setMessages(prev => [...prev, payload.new as Message]);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [announcementId]);

  // Scroll automatico sui nuovi messaggi
  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  async function handleSend() {
    if (!text.trim()) return;
    
    const messageText = text;
    setText("");
    
    try {
      await fetch(`/api/announcements/${announcementId}/messages`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ content: messageText }),
      });
    } catch (error) {
      console.error("Errore invio messaggio:", error);
      setText(messageText); // Ripristina il testo in caso di errore
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex flex-col h-96 border rounded-xl overflow-hidden">
      <div className="flex-1 overflow-auto space-y-2 p-4 bg-neutral-50">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            Nessun messaggio ancora. Inizia la conversazione!
          </div>
        ) : (
          messages.map(m => (
            <div 
              key={m.id} 
              className={cn(
                "max-w-xs rounded-lg px-3 py-2 text-sm",
                m.user_id === currentUserId
                  ? "ml-auto bg-primary-500 text-white"
                  : "mr-auto bg-white border"
              )}
            >
              <div>{m.content}</div>
              <div className={cn(
                "text-xs mt-1 opacity-70",
                m.user_id === currentUserId ? "text-white" : "text-gray-500"
              )}>
                {new Date(m.created_at).toLocaleTimeString()}
              </div>
            </div>
          ))
        )}
        <div ref={bottomRef} />
      </div>
      
      <div className="border-t p-3 flex gap-2 bg-white">
        <Textarea 
          rows={1} 
          value={text} 
          onChange={e => setText(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Scrivi un messaggio..."
          className="flex-1 resize-none"
        />
        <Button 
          onClick={handleSend} 
          disabled={!text.trim()}
          className="bg-primary-700 hover:bg-primary-500"
        >
          Send
        </Button>
      </div>
    </div>
  );
}
