import { ReactNode } from 'react';

interface CategoryMiniCardProps {
  title: string;
  icon?: ReactNode;
}

/**
 * Mini card per categoria con design compatto
 * Hover effect da primary-700 a primary-500
 */
export default function CategoryMiniCard({ title, icon }: CategoryMiniCardProps) {
  return (
    <div className="rounded-xl bg-primary-700 hover:bg-primary-500 text-white flex flex-col items-center justify-center h-32 w-full transition-colors cursor-pointer">
      {icon && (
        <div className="mb-2 text-2xl">
          {icon}
        </div>
      )}
      <h3 className="font-heading font-semibold text-center text-sm">
        {title}
      </h3>
    </div>
  );
}
