import { defineConfig, devices } from '@playwright/test';

/**
 * Configurazione Playwright per test E2E
 * 
 * Questo file configura Playwright per eseguire test end-to-end
 * sull'applicazione ReBuild Link, testando funzionalità critiche
 * come autenticazione, navigazione multi-tenant e CRUD annunci.
 */
export default defineConfig({
  // Directory contenente i test E2E
  testDir: './e2e',
  
  // Timeout per ogni singolo test (30 secondi)
  timeout: 30_000,
  
  // Aspetta che tutti i test finiscano prima di terminare
  fullyParallel: true,
  
  // Fallisce se ci sono test che non dovrebbero essere committati
  forbidOnly: !!process.env.CI,
  
  // Retry automatico in caso di fallimento (solo in CI)
  retries: process.env.CI ? 2 : 0,
  
  // Numero di worker paralleli
  workers: process.env.CI ? 1 : undefined,
  
  // Reporter per output dei test
  reporter: [
    ['html'],
    ['list'],
    ...(process.env.CI ? [['github'] as const] : [])
  ],
  
  // Configurazione globale per tutti i test
  use: {
    // URL base dell'applicazione (locale o staging)
    baseURL: process.env.E2E_BASE_URL || 'http://localhost:3000',
    
    // Esegui in modalità headless (senza interfaccia grafica)
    headless: true,
    
    // Registra trace per debugging in caso di retry
    trace: 'on-first-retry',
    
    // Screenshot solo in caso di fallimento
    screenshot: 'only-on-failure',
    
    // Registra video solo in caso di fallimento
    video: 'retain-on-failure',
    
    // Timeout per azioni individuali (click, type, etc.)
    actionTimeout: 10_000,
    
    // Timeout per navigazione tra pagine
    navigationTimeout: 15_000,
  },

  // Configurazione progetti per diversi browser/dispositivi
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    
    // Decommentare per testare su Firefox
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },
    
    // Decommentare per testare su Safari
    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },
    
    // Test mobile (decommentare se necessario)
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
  ],

  // Server di sviluppo locale (opzionale)
  // Playwright può avviare automaticamente il server per i test
  webServer: process.env.CI ? undefined : {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120_000,
  },
});
