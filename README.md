# ReBuild Link

Marketplace multi‑tenant per la ricostruzione in contesti complessi (prima istanza: **Ucraina**). Piattaforma web + API + asset dati.

## 🚀 Quick Start

```bash
# Installa le dipendenze
npm install

# Avvia il server di sviluppo
npm run dev

# Build per produzione
npm run build
```

**Nota importante**: Se riscontri errori 404 durante lo sviluppo, assicurati che non ci sia una cartella `app` vuota nella root del progetto. Next.js 15 con App Router richiede che le pagine siano in `src/app/`.

---

## 🌐 Visione

Un unico hub dove – in qualunque area post‑crisi – aziende internazionali possono:

- trovare fornitori di mezzi, materiali, logistica
- reclutare personale locale/estero (job request)
- accedere a report di mercato basati sui dati generati.

Il codice è **riutilizzabile**: ogni territorio è un _tenant_ isolato tramite `tenant_id`.

---

## 🔧 Stack Tecnologico

| Layer                    | Scelte                                                    |
| ------------------------ | --------------------------------------------------------- |
| **Frontend**             | Next.js 15 (App Router) · React · TypeScript · Tailwind 3 |
| **Backend (API Routes)** | Node (Next API) · pg Pool                                 |
| **Auth / Storage**       | Supabase (GoTrue, Storage)                                |
| **Database**             | PostgreSQL 15 (Docker → AWS RDS in prod)                  |
| **CI/CD**                | GitHub Actions → Vercel (frontend)                        |
| **Dev tools**            | VS Code + Copilot                                         |

---

## 📚 Schema dati essenziale

```
tenants  (id, slug, name, default_locale, ...)
users    (id, tenant_id, user_type, ...)
announcements (id, tenant_id, creator_id, type, ...)
announcement_categories (announcement_id, category_id)
messages, ratings, event_logs ...
```

Multi‑tenancy via colonna `tenant_id` + middleware che legge lo slug URL.

---

## 🛣️ API (MVP)

| Verbo    | Rotta                     | Descrizione                        | Parametri                           |
| -------- | ------------------------- | ---------------------------------- | ----------------------------------- |
| **GET**  | `/api/announcements`      | Lista ultimi 20 annunci del tenant | `?tenant=ua` o header `x-tenant-id` |
| **POST** | `/api/announcements`      | Crea nuovo annuncio _(auth)_       | Header `x-tenant-id` + JWT          |
| **GET**  | `/api/announcements/[id]` | Dettaglio singolo annuncio         | -                                   |
| **POST** | `/api/auth/signup`        | Registrazione Supabase             | `{email, password}`                 |
| **POST** | `/api/auth/login`         | Login Supabase                     | `{email, password}`                 |

**Caratteristiche**:

- Validazione payload tramite `src/lib/validators.ts` (Zod)
- Auth protetta da middleware JWT (`src/middleware/auth.ts`)
- Multi-tenancy via query params (`?tenant=ua`) o header (`x-tenant-id`)
- Mapping automatico slug → tenant_id (`ua` → `1`, `it` → `2`)

---

## 🚀 Avvio locale

```bash
# 1. Clona repo e installa dipendenze
cd app && npm install

# 2. Avvia Postgres in Docker
cd ..
docker run --name rebuild-postgres -e POSTGRES_PASSWORD=pass123 -e POSTGRES_DB=rebuildlink -p 5432:5432 -d postgres:15

# 3. Migrazione schema
psql "postgresql://postgres:pass123@localhost:5432/rebuildlink" -f db/migrations/001_schema.sql

# 4. Seed demo (categorie + annunci)
docker cp db/seed.sql rebuild-postgres:/tmp/seed.sql
docker exec -it rebuild-postgres psql -U postgres -d rebuildlink -f /tmp/seed.sql

# 5. Variabili ambiente
# Crea il file .env.local con le seguenti variabili:
cat > app/.env.local << EOF
DATABASE_URL=postgresql://postgres:pass123@localhost:5432/rebuildlink

# Supabase per client-side (frontend)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Supabase per server-side (API routes)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
EOF

# 6. Dev server
cd app && npm run dev  # http://localhost:3000 (o porta disponibile)
```

---

## 🗂 Architettura cartelle (app/)

```
app/
 ├─ src/
 │   ├─ app/                    # App Router (Next.js 15)
 │   │   ├─ (auth)/signin/      # Gruppo route autenticazione
 │   │   ├─ [slug]/             # Route dinamiche tenant (ua, it)
 │   │   │   ├─ annunci/        # Lista annunci per tenant
 │   │   │   │   ├─ [id]/       # Dettaglio singolo annuncio
 │   │   │   │   └─ new/        # Form nuovo annuncio
 │   │   │   └─ layout.tsx      # Layout specifico tenant
 │   │   ├─ api/                # API Routes server-side
 │   │   │   ├─ auth/           # Endpoint autenticazione
 │   │   │   └─ announcements/  # CRUD annunci
 │   │   ├─ layout.tsx          # Layout globale
 │   │   ├─ page.tsx            # Home (redirect a /signin)
 │   │   └─ globals.css         # Stili Tailwind
 │   ├─ components/             # Componenti React riutilizzabili
 │   │   ├─ LocaleContext.tsx   # Context multilingua (en/ua)
 │   │   ├─ LocaleToggle.tsx    # Toggle lingua header
 │   │   ├─ AnnouncementsList.tsx # Lista annunci con SWR
 │   │   └─ ProtectedPage.tsx   # HOC per route protette
 │   ├─ lib/                    # Utilities e configurazioni
 │   │   ├─ supabaseClient.ts   # Client Supabase frontend
 │   │   ├─ db.ts               # Pool PostgreSQL
 │   │   ├─ validators.ts       # Schemi Zod validazione
 │   │   └─ authHelper.ts       # Helper autenticazione
 │   ├─ middleware/             # Middleware server-side
 │   │   └─ auth.ts             # Middleware JWT per API
 │   ├─ i18n/                   # Internazionalizzazione
 │   │   ├─ en.ts               # Traduzioni inglesi
 │   │   └─ ua.ts               # Traduzioni ucraine
 │   ├─ types/                  # Tipi TypeScript condivisi
 │   └─ test/                   # Test Jest
 ├─ public/                     # Asset statici
 ├─ db/                         # Database schema e seed
 │   └─ seed.sql                # Dati demo
 ├─ .env.local                  # Variabili ambiente (git-ignored)
 ├─ package.json                # Dipendenze e script
 ├─ tsconfig.json               # Configurazione TypeScript
 ├─ tailwind.config.js          # Configurazione Tailwind CSS
 └─ next.config.ts              # Configurazione Next.js 15
```

---

## ✨ Funzionalità Implementate

### 🎯 **Frontend (React + Next.js 15)**

- ✅ **Routing Multi-tenant**: Route dinamiche `/[slug]/annunci` (ua, it)
- ✅ **Autenticazione UI**: Pagina signin con Supabase Auth UI
- ✅ **Internazionalizzazione**: Context React per EN/UA con toggle header
- ✅ **Layout Responsive**: Tailwind CSS con font Google Geist
- ✅ **Lista Annunci**: Componente con SWR per fetch ottimizzato
- ✅ **Form Annunci**: Pagine per creazione e dettaglio annunci

### 🔧 **Backend (API Routes)**

- ✅ **API Multi-tenant**: Supporto query params (`?tenant=ua`) e header
- ✅ **Autenticazione**: Endpoint login/signup con Supabase
- ✅ **CRUD Annunci**: API complete per gestione annunci
- ✅ **Middleware JWT**: Protezione route con validazione token
- ✅ **Validazione Zod**: Schema validation per tutti i payload

### 🏗️ **Architettura**

- ✅ **Next.js 15 App Router**: Struttura moderna con Server Components
- ✅ **TypeScript Strict**: Tipizzazione completa e type safety
- ✅ **Database Pool**: Connessione PostgreSQL ottimizzata
- ✅ **Multi-tenancy**: Isolamento dati via `tenant_id`
- ✅ **Build System**: ESLint + TypeScript + Tailwind integrati

### 🔄 **Migliorie Tecniche Recenti**

- ✅ **Query Params API**: Migrazione da header a query params per pulizia
- ✅ **Mapping Centralizzato**: Slug → tenant_id gestito lato server
- ✅ **Compatibilità Next.js 15**: Parametri asincroni per route dinamiche
- ✅ **Retrocompatibilità**: API supporta sia query params che header
- ✅ **Error Handling**: Gestione errori robusta con logging

---

## 🏁 Roadmap (Q3 2025)

| Fase          | Obiettivo                       | Stato |
| ------------- | ------------------------------- | ----- |
| **MVP 1**     | Home + lista annunci (GET)      | ✅    |
| **MVP 2**     | POST /announcements + Form FE   | ✅    |
| **MVP 3**     | Auth UI + route protette        | ✅    |
| **MVP 4**     | Chat realtime (Supabase)        | 🔜    |
| **CI/CD**     | GitHub Actions + Vercel preview | ◻︎    |
| **Analytics** | Dashboard trend / report export | ◻︎    |

---

## � Comandi di Sviluppo

```bash
# Sviluppo
npm run dev          # Avvia server di sviluppo
npm run build        # Build di produzione
npm run start        # Avvia build di produzione
npm run lint         # Controllo ESLint

# Testing
npm run test         # Esegui test Jest (unit tests)
npm run test:watch   # Test in modalità watch
npm run e2e          # Esegui test E2E con Playwright

# Database (Docker)
npm run db:up        # Avvia PostgreSQL container
npm run db:down      # Ferma PostgreSQL container

# Database (AWS RDS)
./scripts/test_scripts.sh    # Testa script e dipendenze
./scripts/create_rds.sh      # Crea istanza RDS PostgreSQL
./scripts/check_rds_status.sh # Controlla stato istanza RDS
./scripts/run_migrations.sh  # Esegui migrazioni database
./scripts/rds_monitor.sh     # Configura allarmi CloudWatch per RDS
./scripts/delete_rds.sh      # Elimina istanza RDS (ATTENZIONE!)

# Secrets & Deployment
./scripts/check_env_vars.sh     # Verifica variabili d'ambiente
./scripts/set_github_secrets.sh # Configura GitHub Actions secrets
./scripts/setup_vercel_secrets.sh # Configura secrets Vercel per CI/CD
./scripts/bootstrap_cli_project.sh # Bootstrap completo da zero a cloud
```

### 🔍 **URL di Test**

- **Home**: `http://localhost:3000` → redirect a `/signin`
- **Signin**: `http://localhost:3000/signin`
- **Annunci UA**: `http://localhost:3000/ua/annunci`
- **Annunci IT**: `http://localhost:3000/it/annunci`
- **API Annunci**: `http://localhost:3000/api/announcements?tenant=ua`

---

## �🤝 Contribuire

1. Apri una issue o un thread dedicato (#frontend, #backend, ecc.).
2. Mantieni PR piccole; un task → un commit.
3. **Sempre eseguire prima del push**:
   ```bash
   npm run lint && npm run build
   ```
4. Testa le funzionalità su entrambi i tenant (ua, it).

---

## 🚀 Provisioning del database su AWS

> In questa sezione impariamo a spostare il nostro DB Postgres dal
> laptop al cloud utilizzando Amazon RDS.

### 1. Esegui lo script

```bash
# Scegli una password sicura
export DB_PASSWORD="SuperSegreta123!"
./scripts/create_rds.sh
```

### 2. Cosa fa lo script?

1. **Rileva il tuo IP pubblico** per restringere l'accesso.

2. **Crea (o riutilizza) una Security Group** che apre solo la porta 5432 al tuo IP.

3. **Provisiona un'istanza PostgreSQL 15** free-tier (db.t3.micro, 20 GB) nella regione eu-central-1.

4. **Abilita backup automatici** giornalieri (7 giorni).

Una volta che AWS segna l'istanza come AVAILABLE, copia il suo endpoint
e costruisci la tua DATABASE_URL in formato:

```
postgres://<DB_USER>:<DB_PASSWORD>@<ENDPOINT>:5432/<DB_NAME>
```

💡 **Tip**: Conserva questa variabile per i prossimi step di CI/CD.

### 3. Configurazione ambiente produzione

Dopo aver ottenuto l'endpoint RDS, aggiorna il tuo `.env.local` (o `.env.production`):

```bash
# Database di produzione su AWS RDS
DATABASE_URL=postgres://rebuildlink_user:<EMAIL>:5432/rebuildlink

# Mantieni le altre variabili Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 4. Migrazione schema su RDS

Una volta che l'istanza è pronta, esegui la migrazione:

```bash
# Connettiti al database RDS e crea lo schema
psql "**************************************************************/rebuildlink" -f db/migrations/001_schema.sql

# Opzionale: carica i dati demo
psql "**************************************************************/rebuildlink" -f db/seed.sql
```

### 5. Script di gestione aggiuntivi

```bash
# Controlla lo stato dell'istanza RDS
./scripts/check_rds_status.sh

# Monitora in tempo reale (ogni 30 secondi)
watch -n 30 './scripts/check_rds_status.sh'

# Elimina l'istanza quando non serve più (ATTENZIONE: irreversibile!)
./scripts/delete_rds.sh
```

### 6. Vantaggi del database in cloud

- ✅ **Backup automatici**: Snapshot giornalieri per 7 giorni
- ✅ **Scalabilità**: Possibilità di aumentare storage e compute
- ✅ **Sicurezza**: Accesso limitato al tuo IP + VPC isolata
- ✅ **Monitoring**: CloudWatch metrics integrate
- ✅ **Multi-AZ**: Disponibilità elevata (upgrade futuro)
- ✅ **Gestione semplificata**: Script automatizzati per provisioning e cleanup

---

## 🔑 Configurazione Secrets GitHub & Vercel

> In questa sezione impariamo a gestire in modo sicuro le variabili d'ambiente
> sensibili per GitHub Actions (CI/CD) e Vercel (hosting).

### 1. GitHub Actions Secrets

I **GitHub Secrets** sono variabili d'ambiente crittografate che vengono utilizzate nei workflow CI/CD senza esporre valori sensibili nel codice.

```bash
# Esporta le variabili (usa i tuoi valori reali)
export DATABASE_URL="**************************************************/rebuildlink"
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="eyJhbGciOi..."

# Salvale nel repo GitHub tramite script automatizzato
./scripts/set_github_secrets.sh
```

**Cosa fa lo script:**

1. **Valida prerequisiti**: Controlla che GitHub CLI sia installato e autenticato
2. **Verifica variabili**: Assicura che tutte le variabili richieste siano esportate
3. **Identifica repository**: Rileva automaticamente il repo GitHub corrente
4. **Salva secrets**: Utilizza `gh secret set` per crittografare e salvare i valori
5. **Conferma successo**: Mostra un riepilogo delle operazioni completate

### 2. Configurazione Vercel

Per il deployment su Vercel, le variabili d'ambiente devono essere configurate nella dashboard del progetto.

```bash
# Copia il template e personalizzalo
cp vercel.env.example vercel.env.local

# Modifica il file con i tuoi valori reali
# Poi importa le variabili nella dashboard Vercel
```

**Metodi di configurazione:**

**A) Dashboard Vercel (Raccomandato):**

1. Vai su [vercel.com](https://vercel.com) > Il tuo progetto
2. Settings > Environment Variables
3. Aggiungi manualmente ogni variabile:
   - `DATABASE_URL` → Il tuo endpoint RDS
   - `SUPABASE_URL` → URL del progetto Supabase
   - `SUPABASE_ANON_KEY` → Chiave pubblica Supabase

**B) Vercel CLI:**

```bash
# Installa Vercel CLI
npm i -g vercel

# Login e deploy
vercel login
vercel --prod
```

### 3. Variabili Richieste

| Variabile                   | Descrizione                | Dove Ottenerla                      | Sicurezza   |
| --------------------------- | -------------------------- | ----------------------------------- | ----------- |
| `DATABASE_URL`              | Connessione PostgreSQL RDS | `./scripts/check_rds_status.sh`     | 🔒 Segreta  |
| `SUPABASE_URL`              | URL progetto Supabase      | Dashboard Supabase > Settings > API | 🌐 Pubblica |
| `SUPABASE_ANON_KEY`         | Chiave anonima Supabase    | Dashboard Supabase > Settings > API | 🌐 Pubblica |
| `SUPABASE_SERVICE_ROLE_KEY` | Chiave service role        | Dashboard Supabase > Settings > API | 🔒 Segreta  |

### 4. Best Practices per la Sicurezza

- ✅ **Mai committare** file `.env` con valori reali
- ✅ **Usa prefisso** `NEXT_PUBLIC_` solo per variabili sicure da esporre
- ✅ **Mantieni sincronizzati** GitHub Secrets e Vercel Environment Variables
- ✅ **Testa sempre** dopo aver aggiornato le variabili
- ✅ **Ruota periodicamente** le chiavi sensibili (DATABASE_URL, SERVICE_ROLE_KEY)

### 5. Troubleshooting Comune

**Problema**: App non si connette al database

```bash
# Verifica che DATABASE_URL sia corretta
./scripts/check_rds_status.sh
```

**Problema**: Autenticazione Supabase non funziona

```bash
# Controlla che SUPABASE_URL e SUPABASE_ANON_KEY siano corrette
# Dashboard Supabase > Settings > API
```

**Problema**: API server-side falliscono

```bash
# Verifica SUPABASE_SERVICE_ROLE_KEY
# Assicurati che sia configurata solo come secret (non NEXT_PUBLIC_)
```

💡 **Suggerimento didattico**: Mantieni sempre sincronizzati i secrets tra GitHub (per i workflow CI) e Vercel (per l'app in esecuzione) per evitare incongruenze di ambiente.

---

## 📊 Monitoraggio RDS con CloudWatch

> In questa sezione implementiamo un sistema di monitoraggio automatico per l'istanza
> PostgreSQL RDS con allarmi CloudWatch e notifiche SNS via email.

### 1. Configurazione Allarmi Automatici

Il sistema di monitoraggio si attiva automaticamente durante il deploy in produzione tramite GitHub Actions:

```bash
# Configurazione manuale (opzionale)
export RDS_INSTANCE_ID="rebuildlink-db"
export ALERT_EMAIL="<EMAIL>"
./scripts/rds_monitor.sh
```

**Cosa monitora lo script:**

- ✅ **CPU Utilization** → Allarme se > 80% per 5 minuti consecutivi
- ✅ **SNS Topic** → Crea/aggiorna topic per notifiche email
- ✅ **CloudWatch Alarm** → Configura soglie e azioni automatiche
- ✅ **Email Subscription** → Iscrive automaticamente l'email agli alert

### 2. Configurazione Secrets per Monitoraggio

Aggiungi questi secrets al repository GitHub per abilitare il monitoraggio automatico:

```bash
# Secrets richiesti per il monitoraggio RDS
gh secret set RDS_INSTANCE_ID --body "rebuildlink-db"
gh secret set ALERT_EMAIL --body "<EMAIL>"
gh secret set AWS_ACCESS_KEY_ID --body "AKIA..."
gh secret set AWS_SECRET_ACCESS_KEY --body "..."
gh secret set AWS_DEFAULT_REGION --body "eu-central-1"
```

### 3. Workflow CI/CD Integrato

Il monitoraggio è integrato nella pipeline CI/CD e si attiva solo sui deploy in produzione:

**Flusso automatico:**

1. **Push su main** → Trigger workflow GitHub Actions
2. **Deploy Vercel** → App pubblicata in produzione
3. **Setup Monitoring** → Allarmi CloudWatch configurati automaticamente
4. **Email Confirmation** → Conferma iscrizione SNS via email

### 4. Tipi di Allarmi Configurati

| Metrica                  | Soglia | Periodo | Azione                    |
| ------------------------ | ------ | ------- | ------------------------- |
| **CPU Utilization**      | > 80%  | 5 min   | Email + Log CloudWatch    |
| **Database Connections** | > 90%  | 2 min   | Email + Auto-scaling hint |
| **Free Storage Space**   | < 2GB  | 10 min  | Email + Backup reminder   |

### 5. Gestione Notifiche

**Conferma iscrizione email:**

1. Dopo il primo deploy, riceverai un'email di conferma da AWS SNS
2. Clicca "Confirm subscription" per attivare le notifiche
3. Gli allarmi futuri arriveranno direttamente nella tua inbox

**Formato notifiche:**

```
🚨 ALARM: rebuildlink-db-HighCPU in EU-CENTRAL-1
Reason: Threshold Crossed: 1 out of 1 datapoints [85.2] was greater than the threshold (80.0).
Time: 2025-01-09T14:30:00.000Z
```

### 6. Dashboard CloudWatch

**Accesso dashboard:**

- URL: `https://console.aws.amazon.com/cloudwatch/`
- Sezione: **Alarms** → Visualizza tutti gli allarmi attivi
- Metriche: **RDS** → Grafici dettagliati performance database

**Metriche chiave da monitorare:**

- ✅ **CPUUtilization**: Carico processore
- ✅ **DatabaseConnections**: Connessioni attive
- ✅ **FreeStorageSpace**: Spazio disco disponibile
- ✅ **ReadLatency/WriteLatency**: Performance I/O

### 7. Troubleshooting Allarmi

**Allarme CPU alto persistente:**

```bash
# Analizza query lente
psql "$DATABASE_URL" -c "
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
ORDER BY total_time DESC LIMIT 10;"

# Ottimizza indici mancanti
EXPLAIN ANALYZE SELECT * FROM announcements WHERE tenant_id = 1;
```

**Connessioni database esaurite:**

```bash
# Controlla connessioni attive
psql "$DATABASE_URL" -c "
SELECT state, count(*)
FROM pg_stat_activity
GROUP BY state;"

# Aumenta max_connections (se necessario)
# Considera connection pooling (PgBouncer)
```

### 8. Vantaggi del Monitoraggio Automatico

- ✅ **Proattivo**: Rileva problemi prima che impattino gli utenti
- ✅ **Automatizzato**: Zero configurazione manuale dopo setup iniziale
- ✅ **Integrato**: Parte della pipeline CI/CD standard
- ✅ **Scalabile**: Facilmente estendibile con nuove metriche
- ✅ **Didattico**: Insegna best practices di monitoring in produzione

💡 **Suggerimento didattico**: Il monitoraggio proattivo è essenziale per applicazioni in produzione. CloudWatch + SNS forniscono un sistema robusto e scalabile per rilevare e notificare problemi prima che diventino critici.

---

## 🎭 Test End-to-End con Playwright

> In questa sezione implementiamo test E2E automatizzati che verificano il funzionamento
> completo dell'applicazione dal punto di vista dell'utente finale.

### 1. Configurazione Test E2E

I test E2E utilizzano Playwright per simulare interazioni utente reali:

```bash
# Esegui tutti i test E2E
npm run e2e

# Esegui test in modalità debug
npx playwright test --debug

# Esegui test con interfaccia grafica
npx playwright test --ui

# Genera report HTML
npx playwright show-report
```

**Configurazione automatica:**

- ✅ **Browser Chromium**: Installato automaticamente
- ✅ **Server locale**: Avviato automaticamente per i test
- ✅ **Screenshots**: Catturati in caso di fallimento
- ✅ **Video**: Registrati per debugging

### 2. Struttura Test E2E

```
e2e/
├── homepage.spec.ts          # Test homepage e navigazione
├── announcements.spec.ts     # Test funzionalità annunci
└── auth.spec.ts              # Test autenticazione (futuro)
```

**Categorie di test implementate:**

| Categoria       | Descrizione                             | File                    |
| --------------- | --------------------------------------- | ----------------------- |
| **Homepage**    | Caricamento, redirect, navigazione base | `homepage.spec.ts`      |
| **Annunci**     | CRUD annunci, API, multi-tenancy        | `announcements.spec.ts` |
| **API**         | Endpoint REST, validazione risposte     | Entrambi                |
| **Responsive**  | Mobile/desktop, viewport                | `homepage.spec.ts`      |
| **Performance** | Tempi di caricamento                    | `announcements.spec.ts` |

### 3. Test Implementati

#### **Homepage e Navigazione**

```typescript
// Test caricamento homepage
test("homepage loads correctly", async ({ page }) => {
  await page.goto("/");
  const title = await page.title();
  expect(title).toBeTruthy();
});

// Test navigazione multi-tenant
test("navigation to tenant pages works", async ({ page }) => {
  await page.goto("/ua/annunci");
  await expect(page).toHaveURL("/ua/annunci");
});
```

#### **API e Backend**

```typescript
// Test endpoint API
test("API endpoints respond correctly", async ({ page }) => {
  const response = await page.request.get("/api/announcements?tenant=ua");
  expect([200, 401, 404]).toContain(response.status());
});
```

#### **Multi-tenancy**

```typescript
// Test isolamento tenant
test("different tenants show different content", async ({ page }) => {
  await page.goto("/ua/annunci");
  const uaContent = await page.textContent("body");

  await page.goto("/it/annunci");
  const itContent = await page.textContent("body");

  expect(uaContent).not.toBe(itContent);
});
```

### 4. Integrazione CI/CD

I test E2E sono integrati nella pipeline GitHub Actions:

**Flusso automatico:**

1. **Build applicazione** → Compila versione produzione
2. **Avvia server** → Server Next.js su porta 3000
3. **Installa browser** → Chromium per Playwright
4. **Esegui test E2E** → Tutti i test automaticamente
5. **Genera report** → Screenshot e video per debugging

**Configurazione workflow:**

```yaml
- name: 🎭 Installa Playwright browsers
  run: npx playwright install --with-deps chromium

- name: 🧑‍💻 Esegui test E2E con Playwright
  run: |
    npm run start &
    SERVER_PID=$!
    npx wait-on http://localhost:3000 --timeout 60000
    npm run e2e
    kill $SERVER_PID || true
```

### 5. Debugging e Troubleshooting

**Visualizzare test in esecuzione:**

```bash
# Modalità headed (con interfaccia grafica)
npx playwright test --headed

# Debug step-by-step
npx playwright test --debug

# Test specifico
npx playwright test homepage.spec.ts
```

**Analizzare fallimenti:**

```bash
# Report HTML con screenshot e video
npx playwright show-report

# Trace viewer per debugging dettagliato
npx playwright show-trace test-results/trace.zip
```

**File generati automaticamente:**

- `test-results/` → Screenshot, video, trace
- `playwright-report/` → Report HTML
- `test-results/` → Log dettagliati

### 6. Best Practices Test E2E

#### **Selettori Robusti**

```typescript
// ✅ Buono: usa data-testid
await page.locator('[data-testid="announcement-item"]').click();

// ✅ Buono: usa ruoli semantici
await page.getByRole("button", { name: "Crea Annuncio" }).click();

// ❌ Evita: selettori CSS fragili
await page.locator(".btn-primary").click();
```

#### **Attese Intelligenti**

```typescript
// ✅ Buono: aspetta elemento specifico
await expect(page.locator('[data-testid="loading"]')).toBeHidden();

// ✅ Buono: aspetta stato di rete
await page.waitForLoadState("networkidle");

// ❌ Evita: timeout fissi
await page.waitForTimeout(5000);
```

#### **Test Isolati**

```typescript
// ✅ Ogni test è indipendente
test("create announcement", async ({ page }) => {
  // Setup dati test
  // Esegui azione
  // Verifica risultato
  // Cleanup automatico
});
```

### 7. Metriche e Reporting

**Metriche automatiche:**

- ✅ **Tempo esecuzione**: Per ogni test
- ✅ **Success rate**: Percentuale successo
- ✅ **Coverage E2E**: Funzionalità testate
- ✅ **Performance**: Tempi caricamento pagine

**Report generati:**

- **HTML Report**: Risultati dettagliati con media
- **JUnit XML**: Per integrazione CI/CD
- **JSON**: Per analisi programmatica

### 8. Vantaggi Test E2E

- ✅ **Confidence**: Verifica funzionamento completo
- ✅ **Regression**: Rileva regressioni automaticamente
- ✅ **Cross-browser**: Testa su diversi browser
- ✅ **Real scenarios**: Simula utenti reali
- ✅ **CI Integration**: Blocca deploy se test falliscono

💡 **Suggerimento didattico**: I test E2E sono il livello più alto della piramide dei test. Costano di più da mantenere ma forniscono la massima confidence che l'applicazione funzioni correttamente per gli utenti finali.

---

## ⚙️ CI/CD con GitHub Actions

> In questa sezione implementiamo una pipeline completa di Continuous Integration
> e Continuous Deployment che automatizza test, build e deploy ad ogni commit.

### 1. Workflow Automatizzato

Il file `.github/workflows/ci.yml` definisce una pipeline che si attiva automaticamente:

**Trigger:**

- ✅ **Push su `main`** → Deploy in produzione
- ✅ **Pull Request** → Deploy preview per testing

**Fasi del workflow:**

1. **Setup** → Prepara ambiente Node.js 20 con cache npm
2. **Quality** → Esegue lint (ESLint) e test (Jest)
3. **Build** → Compila versione produzione Next.js
4. **Deploy** → Pubblica su Vercel (prod o preview)
5. **Notify** → Conferma successo o segnala errori

### 2. Configurazione Secrets Vercel

Oltre ai secrets base, il workflow richiede credenziali Vercel:

```bash
# Configura automaticamente i secrets Vercel
./scripts/setup_vercel_secrets.sh
```

**Secrets aggiuntivi configurati:**

- `VERCEL_TOKEN` → Token personale per autenticazione
- `VERCEL_ORG_ID` → ID organizzazione Vercel
- `VERCEL_PROJECT_ID` → ID progetto specifico

### 3. Flusso di Sviluppo

**Sviluppo locale:**

```bash
# 1. Crea feature branch
git checkout -b feature/nuova-funzionalita

# 2. Sviluppa e testa localmente
npm run dev
npm run test
npm run lint

# 3. Commit e push
git add .
git commit -m "feat: aggiunge nuova funzionalità"
git push origin feature/nuova-funzionalita
```

**Review e Deploy:**

```bash
# 4. Crea Pull Request su GitHub
# → Trigger automatico: lint + test + build + deploy preview

# 5. Review del codice e merge su main
# → Trigger automatico: deploy produzione
```

### 4. Monitoraggio Pipeline

**Dashboard GitHub Actions:**

- Vai su `https://github.com/TUO-USERNAME/REPO/actions`
- Monitora stato di ogni workflow
- Visualizza log dettagliati per debugging

**Notifiche automatiche:**

- ✅ **Successo**: URL deploy + dettagli commit
- ❌ **Fallimento**: Log errori + suggerimenti risoluzione

### 5. Vantaggi della Pipeline

- ✅ **Qualità garantita**: Nessun deploy senza test passati
- ✅ **Deploy automatico**: Zero intervento manuale
- ✅ **Preview sicure**: Test su URL temporanei prima della produzione
- ✅ **Rollback rapido**: Cronologia deploy per ripristini veloci
- ✅ **Feedback immediato**: Notifiche in tempo reale su stato deploy

### 6. Troubleshooting CI/CD

**Workflow fallisce al lint:**

```bash
# Risolvi errori localmente
npm run lint -- --fix
git add . && git commit -m "fix: risolve errori lint"
```

**Test non passano:**

```bash
# Esegui test localmente per debug
npm run test -- --verbose
# Correggi e ricommit
```

**Deploy Vercel fallisce:**

```bash
# Verifica secrets configurati
gh secret list --repo TUO-USERNAME/REPO
# Ricontrolla configurazione Vercel
./scripts/setup_vercel_secrets.sh
```

💡 **Suggerimento didattico**: La pipeline CI/CD trasforma ogni commit in un potenziale release, forzando buone pratiche di sviluppo e garantendo qualità costante del codice in produzione.

### 7. Automigrazioni Database

Il workflow CI esegue automaticamente tutti i file `migrations/*.sql` contro l'istanza RDS **prima** del deploy, garantendo che lo schema sia sempre allineato con il codice applicativo.

**Sistema di tracking intelligente:**

- ✅ **Tabella `schema_migrations`**: Traccia migrazioni già applicate
- ✅ **Checksum verification**: Rileva modifiche ai file esistenti
- ✅ **Transazioni atomiche**: Rollback automatico in caso di errore
- ✅ **Ordinamento automatico**: Esecuzione in ordine alfabetico/cronologico

**Workflow migrazioni:**

```bash
# 1. Aggiungi nuovo file SQL con naming convention
echo "ALTER TABLE users ADD COLUMN bio TEXT;" > migrations/$(date +%Y%m%d)_add_user_bio.sql

# 2. Commit e push
git add migrations/$(date +%Y%m%d)_add_user_bio.sql
git commit -m "feat(db): add bio column to users"
git push

# → GitHub Actions applicherà automaticamente la migrazione
```

**Naming convention raccomandata:**

```
migrations/
├── 001_initial_schema.sql          # Schema iniziale
├── 002_add_categories.sql           # Aggiunta tabelle
├── 20250109_add_user_bio.sql        # Con timestamp
├── 20250110_create_indexes.sql      # Ottimizzazioni
└── 20250115_add_notifications.sql   # Nuove funzionalità
```

**Gestione locale:**

```bash
# Esegui migrazioni localmente
export DATABASE_URL="postgres://user:pass@localhost:5432/rebuildlink"
./scripts/run_migrations.sh

# Verifica stato migrazioni
psql "$DATABASE_URL" -c "SELECT filename, applied_at FROM schema_migrations ORDER BY applied_at;"
```

**Vantaggi del sistema:**

- ✅ **Versionamento schema**: Evoluzione database tracciata nel git
- ✅ **Riproducibilità**: Stesso schema in dev, staging, produzione
- ✅ **Zero downtime**: Migrazioni non bloccanti quando possibile
- ✅ **Rollback sicuro**: Cronologia completa per ripristini
- ✅ **Team sync**: Tutti i developer hanno stesso schema

💡 **Tip didattico**: Mantenere le migrazioni versionate nel repo permette di tracciare l'evoluzione dello schema e di riprodurre lo stato DB in ogni ambiente (dev, staging, prod) in modo deterministico.

---

**Let’s rebuild, together.**

## ⛳ Bootstrap Completo (Da Zero a Cloud)

> Script finale che automatizza completamente il setup del progetto:
> da cartella locale a produzione cloud con un solo comando.

### 1. Setup Automatizzato Completo

```bash
# Esporta le variabili richieste
export DATABASE_URL="******************************/db"
export SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_ANON_KEY="eyJhbGciOi..."

# (Opzionale) Personalizza nomi progetto
export GH_REPO_NAME="my-awesome-app"
export VERCEL_PROJECT_NAME="my-awesome-app"
export GH_PRIVATE=false  # Per repository pubblico

# Avvia bootstrap completo!
./scripts/bootstrap_cli_project.sh
```

### 2. Cosa Fa lo Script Bootstrap

**Fase 1 - Validazione:**

- ✅ Controlla prerequisiti (git, gh, vercel)
- ✅ Verifica autenticazione GitHub e Vercel
- ✅ Valida variabili d'ambiente richieste

**Fase 2 - Repository GitHub:**

- ✅ Crea repository GitHub (pubblico/privato)
- ✅ Inizializza Git se necessario
- ✅ Effettua push iniziale del codice

**Fase 3 - GitHub Actions:**

- ✅ Configura tutti i secrets necessari
- ✅ Attiva pipeline CI/CD automatica

**Fase 4 - Progetto Vercel:**

- ✅ Crea o collega progetto Vercel
- ✅ Configura framework Next.js
- ✅ Gestisce team/organizzazioni

**Fase 5 - Variabili Vercel:**

- ✅ Configura variabili per production
- ✅ Configura variabili per preview
- ✅ Sincronizza con GitHub secrets

**Fase 6 - CI/CD Integration:**

- ✅ Configura token Vercel per deploy automatico
- ✅ Attiva pipeline completa

### 3. Prerequisiti

**Software richiesto:**

```bash
# Git (version control)
git --version

# GitHub CLI (repository management)
gh auth login

# Vercel CLI (deployment)
npm i -g vercel
vercel login
```

**Variabili d'ambiente:**

- `DATABASE_URL` → Endpoint RDS PostgreSQL
- `SUPABASE_URL` → URL progetto Supabase
- `SUPABASE_ANON_KEY` → Chiave pubblica Supabase

### 4. Personalizzazione

**Variabili opzionali:**

```bash
export GH_REPO_NAME="rebuild-link-marketplace"    # Nome repository
export GH_PRIVATE=true                            # Repository privato
export VERCEL_PROJECT_NAME="rebuild-link"         # Nome progetto Vercel
export VERCEL_TEAM="my-team"                      # Team Vercel (se applicabile)
```

### 5. Risultato Finale

Dopo l'esecuzione avrai:

- 📦 **Repository GitHub** con codice e CI/CD attivo
- 🔑 **Secrets configurati** per GitHub Actions
- 🚀 **Progetto Vercel** pronto per deploy
- 🌍 **Variabili d'ambiente** sincronizzate
- ⚙️ **Pipeline automatica** da commit a produzione

### 6. Test della Pipeline

```bash
# Fai una modifica qualsiasi
echo "# Test" >> README.md

# Commit e push per attivare CI/CD
git add .
git commit -m "feat: test automated pipeline"
git push origin main

# → Automaticamente:
#   • Lint + Test + Build
#   • Automigrazioni database
#   • Deploy produzione Vercel
```

### 7. Monitoraggio

**GitHub Actions:**

- URL: `https://github.com/TUO-USERNAME/REPO/actions`
- Monitora: Lint, test, build, migrazioni, deploy

**Vercel Dashboard:**

- URL: `https://vercel.com/dashboard`
- Monitora: Deploy, performance, domini

### 8. Vantaggi del Bootstrap

- ✅ **Zero configurazione manuale**: Tutto automatizzato
- ✅ **Best practices integrate**: Configurazione professionale
- ✅ **Errore-proof**: Validazioni e controlli integrati
- ✅ **Riproducibile**: Stesso setup per ogni progetto
- ✅ **Didattico**: Ogni step spiegato e commentato

💡 **Suggerimento finale**: Una volta eseguito il bootstrap, ogni commit diventa un potenziale deploy. La pipeline completa garantisce qualità e sicurezza ad ogni rilascio!

---

## 🏗️ Terraform Infrastructure as Code

> In questa sezione implementiamo Infrastructure as Code (IaC) con Terraform per
> automatizzare completamente il provisioning di AWS RDS, GitHub secrets e Vercel environment variables.

### 1. Panoramica Terraform

Terraform gestisce automaticamente:

- ✅ **AWS RDS PostgreSQL**: Istanza database con backup e monitoring
- ✅ **Security Groups**: Regole di accesso sicure
- ✅ **GitHub Secrets**: Configurazione automatica CI/CD
- ✅ **Vercel Environment Variables**: Sync automatico per deploy
- ✅ **CloudWatch Monitoring**: Allarmi e metriche automatiche

### 2. Struttura Infrastructure

```
infra/terraform/
├── provider.tf           # Configurazione provider (AWS, GitHub, Vercel)
├── variables.tf          # Definizione variabili con validazione
├── network.tf            # VPC, Security Groups, Subnet Groups
├── db.tf                 # RDS PostgreSQL con monitoring
├── secrets.tf            # GitHub Actions secrets + Vercel env vars
├── outputs.tf            # Output informativi post-deployment
└── terraform.tfvars.example  # Template configurazione
```

### 3. Setup Rapido

```bash
# 1. Naviga nella directory Terraform
cd infra/terraform

# 2. Copia e personalizza la configurazione
cp terraform.tfvars.example terraform.tfvars
# Modifica terraform.tfvars con i tuoi valori reali

# 3. Inizializza Terraform
terraform init

# 4. Pianifica il deployment (anteprima modifiche)
terraform plan

# 5. Applica l'infrastruttura
terraform apply
```

**Il comando `terraform apply` creerà:**

- Istanza RDS PostgreSQL con backup automatici
- Security Group con accesso limitato al tuo IP
- Tutti i secrets GitHub per CI/CD
- Tutte le environment variables Vercel
- Monitoring CloudWatch con allarmi

### 4. Configurazione Variabili

#### **Variabili Richieste in terraform.tfvars:**

```hcl
# AWS Configuration
aws_region     = "eu-central-1"
db_password    = "SuperSegreta123!"
allowed_cidr   = "*******/32"  # Il tuo IP pubblico

# GitHub Configuration
github_token   = "ghp_..."
github_owner   = "tuo-username"
github_repo    = "rebuild-link"

# Supabase Configuration
supabase_url      = "https://xxx.supabase.co"
supabase_anon_key = "eyJhbGciOi..."

# Vercel Configuration
vercel_token      = "vercel_pat_..."
vercel_project_id = "prj_..."
```

#### **Come Ottenere i Valori:**

| Variabile             | Come Ottenerla             | Esempio                   |
| --------------------- | -------------------------- | ------------------------- |
| **allowed_cidr**      | `curl ifconfig.me` + `/32` | `*******/32`              |
| **github_token**      | GitHub Settings > Tokens   | `ghp_xxx...`              |
| **supabase_url**      | Supabase Dashboard > API   | `https://xxx.supabase.co` |
| **vercel_token**      | Vercel Account > Tokens    | `vercel_pat_xxx...`       |
| **vercel_project_id** | Vercel Project > Settings  | `prj_xxx...`              |

### 5. Output Post-Deployment

Dopo `terraform apply`, riceverai:

```bash
🎉 Infrastruttura ReBuild Link creata con successo!

📊 Database PostgreSQL:
   Host: rebuildlink-db.xxx.eu-central-1.rds.amazonaws.com
   Port: 5432
   Database: rebuildlink
   Username: rebuild_user

🔗 Connessione diretta:
   psql "******************************************/rebuildlink"

🔐 Secrets configurati:
   ✅ GitHub Actions: 5 secrets
   ✅ Vercel: 7 environment variables

📈 Monitoring:
   ✅ CloudWatch Enhanced Monitoring attivo
   ✅ Performance Insights abilitato
   ✅ Backup automatici (7 giorni)
```

### 6. Gestione Lifecycle

#### **Aggiornamenti**

```bash
# Modifica terraform.tfvars o file .tf
terraform plan   # Anteprima modifiche
terraform apply  # Applica modifiche
```

#### **Distruzione (Attenzione!)**

```bash
# Distrugge TUTTA l'infrastruttura
terraform destroy
```

#### **State Management**

```bash
# Visualizza stato attuale
terraform show

# Lista risorse gestite
terraform state list

# Import risorsa esistente
terraform import aws_db_instance.postgres rebuildlink-db
```

### 7. Best Practices Implementate

#### **Sicurezza**

- ✅ **Secrets crittografati**: Variabili sensibili marcate come `sensitive`
- ✅ **Accesso limitato**: Security Group con CIDR specifico
- ✅ **Crittografia storage**: Database crittografato a riposo
- ✅ **Backup automatici**: Retention 7 giorni con snapshot finali

#### **Monitoring**

- ✅ **Enhanced Monitoring**: Metriche dettagliate ogni 60 secondi
- ✅ **Performance Insights**: Analisi query e performance
- ✅ **CloudWatch Alarms**: CPU e connessioni monitorate
- ✅ **Automated backups**: Finestra di backup ottimizzata

#### **Manutenibilità**

- ✅ **Validazione input**: Controlli automatici sui valori
- ✅ **Output informativi**: Dettagli completi post-deployment
- ✅ **Tagging consistente**: Tag automatici per billing e gestione
- ✅ **Lifecycle management**: Prevenzione distruzione accidentale

### 8. Vantaggi Infrastructure as Code

- ✅ **Riproducibilità**: Stesso setup in dev/staging/prod
- ✅ **Versionamento**: Infrastruttura nel git con history
- ✅ **Automazione**: Zero configurazione manuale
- ✅ **Consistency**: Configurazione standardizzata
- ✅ **Rollback**: Possibilità di tornare a versioni precedenti
- ✅ **Documentation**: Codice auto-documentante

💡 **Suggerimento didattico**: Infrastructure as Code trasforma l'infrastruttura da "pet" (curata manualmente) a "cattle" (gestita programmaticamente), abilitando DevOps scalabile e affidabile.

---

---

## 📈 Performance & Insights

> In questa sezione implementiamo monitoring avanzato delle performance PostgreSQL
> con pg_stat_statements, indici ottimizzati e dashboard Grafana per analisi real-time.

### 1. Attiva pg_stat_statements

La migration `002_perf.sql` abilita l'estensione per il tracking delle query e crea indici mirati per ottimizzare le performance dell'applicazione.

⚠️ **IMPORTANTE**: Su AWS RDS, assicurati che il Parameter Group contenga `pg_stat_statements` in `shared_preload_libraries` prima di eseguire la migrazione.

**Indici creati automaticamente:**

- ✅ **Tenants**: `idx_tenants_subdomain` per lookup multi-tenant
- ✅ **Announcements**: `idx_announcements_tenant_created` per listing
- ✅ **Messages**: `idx_messages_receiver_created` per inbox performance
- ✅ **Search**: `idx_announcements_search` per ricerca full-text
- ✅ **Users**: `idx_users_email`, `idx_users_tenant` per autenticazione

### 2. Dashboard Grafana Locale

Stack di monitoring completo con Docker Compose:

```bash
cd monitor

# Configura connessione PostgreSQL
nano grafana/provisioning/datasources/postgres.yml
# Sostituisci con endpoint RDS reale

# Avvia stack monitoring
docker compose up -d

# Accedi a Grafana
open http://localhost:3001
# Login: admin/admin
```

**Dashboard "ReBuild Link – Postgres Insights" include:**

- 🐌 **Top 10 Slow Queries**: Query più lente con statistiche esecuzione
- 🔗 **Database Connections**: Connessioni attive in tempo reale
- 📊 **Table Statistics**: Insert, update, delete per tabella
- 💾 **Database Size**: Dimensioni tabelle e indici

### 3. Migrazione Automatica

Il file `002_perf.sql` viene applicato automaticamente dal step "🗄️ Esegui migrazioni" nella pipeline CI alla prima push su main.

**Funzionalità della migrazione:**

```sql
-- Abilita tracking query
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Indici strategici per performance
CREATE INDEX idx_announcements_tenant_created
ON announcements (tenant_id, created_at DESC);

-- View per monitoring
CREATE VIEW slow_queries AS
SELECT query, calls, mean_time/1000.0 as mean_seconds
FROM pg_stat_statements ORDER BY mean_time DESC;
```

### 4. Monitoring Stack Completo

#### **Servizi Inclusi**

- **Grafana 10.4.1**: Dashboard e visualizzazione (porta 3001)
- **Promtail 2.9.3**: Log aggregation e forwarding
- **PostgreSQL Exporter**: Metriche Prometheus (porta 9187)

#### **Configurazione Automatica**

- ✅ **Datasource PostgreSQL**: Auto-provisioning con RDS
- ✅ **Dashboard**: Caricamento automatico all'avvio
- ✅ **SSL**: Connessioni sicure a RDS
- ✅ **Persistenza**: Volume per configurazioni Grafana

### 5. Query Performance Analysis

#### **Identificare Query Lente**

```sql
-- Top 10 query più lente
SELECT
  LEFT(query, 100) || '...' AS query_preview,
  calls,
  ROUND(mean_time/1000.0, 3) AS mean_seconds
FROM pg_stat_statements
ORDER BY mean_time DESC LIMIT 10;
```

#### **Statistiche Tabelle**

```sql
-- Tabelle più attive
SELECT tablename, n_live_tup, n_tup_ins, n_tup_upd
FROM pg_stat_user_tables
ORDER BY n_live_tup DESC;
```

#### **Indici Non Utilizzati**

```sql
-- Indici da rimuovere
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes
WHERE idx_scan = 0;
```

### 6. Ottimizzazioni Implementate

#### **Multi-tenant Performance**

```sql
-- Lookup tenant per subdomain (O(log n))
CREATE INDEX idx_tenants_subdomain ON tenants (subdomain);

-- Listing annunci per tenant (composito)
CREATE INDEX idx_announcements_tenant_created
ON announcements (tenant_id, created_at DESC);
```

#### **Search Performance**

```sql
-- Full-text search su titolo e descrizione
CREATE INDEX idx_announcements_search
ON announcements USING gin(
  to_tsvector('english', title || ' ' || COALESCE(description, ''))
);
```

#### **Messaging Performance**

```sql
-- Inbox utente ottimizzata
CREATE INDEX idx_messages_receiver_created
ON messages (receiver_user_id, created_at DESC);

-- Messaggi non letti
CREATE INDEX idx_messages_unread
ON messages (receiver_user_id, created_at DESC)
WHERE read_at IS NULL;
```

### 7. Monitoring Real-time

#### **Dashboard Grafana**

- **Refresh automatico**: 30 secondi
- **Query performance**: Tempo medio, totale, chiamate
- **Connection monitoring**: Connessioni attive vs limiti
- **Storage analysis**: Crescita tabelle e indici

#### **Alerting (Futuro)**

```yaml
# Configurazione alert per query lente
- alert: SlowQueries
  expr: pg_stat_statements_mean_time_seconds > 1.0
  for: 5m
  annotations:
    summary: "Query lenta rilevata"
```

### 8. Best Practices Performance

#### **Query Optimization**

- ✅ **Usa indici compositi**: Per query multi-colonna
- ✅ **Evita SELECT \***: Specifica colonne necessarie
- ✅ **Limita risultati**: Usa LIMIT per paginazione
- ✅ **Analizza query plan**: EXPLAIN ANALYZE per ottimizzazioni

#### **Index Management**

- ✅ **Monitora utilizzo**: Rimuovi indici non utilizzati
- ✅ **Indici parziali**: WHERE clause per subset dati
- ✅ **Maintenance**: VACUUM e ANALYZE regolari
- ✅ **Size monitoring**: Controlla crescita indici

#### **Connection Management**

- ✅ **Connection pooling**: PgBouncer per RDS
- ✅ **Timeout appropriati**: Evita connessioni hanging
- ✅ **Monitoring limiti**: RDS connection limits
- ✅ **Prepared statements**: Per query frequenti

### 9. Troubleshooting Performance

#### **Query Lente**

```bash
# Analizza query specifica
psql "$DATABASE_URL" -c "
EXPLAIN ANALYZE SELECT * FROM announcements
WHERE tenant_id = 1 ORDER BY created_at DESC LIMIT 10;"
```

#### **Connessioni Esaurite**

```bash
# Controlla connessioni attive
psql "$DATABASE_URL" -c "
SELECT state, count(*) FROM pg_stat_activity GROUP BY state;"
```

#### **Storage Issues**

```bash
# Dimensioni tabelle
psql "$DATABASE_URL" -c "
SELECT tablename, pg_size_pretty(pg_total_relation_size(tablename::regclass))
FROM pg_tables WHERE schemaname = 'public';"
```

---

## 🔧 Troubleshooting

### Errore 404 durante sviluppo (`npm run dev`)

**Problema**: Il server Next.js si avvia correttamente ma tutte le pagine restituiscono 404.

**Causa**: Next.js 15 con App Router cerca le pagine prima nella cartella `app/` nella root, poi in `src/app/`. Se esiste una cartella `app/` vuota nella root, Next.js la usa invece di `src/app/`.

**Soluzione**:

```bash
# Rimuovi la cartella app vuota dalla root (se presente)
rmdir app  # Windows
rm -rf app # Linux/Mac

# Riavvia il server di sviluppo
npm run dev
```

**Verifica**: Dopo il riavvio, dovresti vedere nel terminale:

```
○ Compiling / ...
✓ Compiled / in 2.1s (703 modules)
GET / 200 in 2275ms
```

### Errori di build con pagine duplicate

**Problema**: `You cannot have two parallel pages that resolve to the same path`

**Causa**: Pagine duplicate in percorsi diversi che si risolvono alla stessa rotta.

**Soluzione**: Rimuovi le pagine duplicate, mantenendo solo quella nel percorso corretto (preferibilmente nei route groups come `(auth)`).

### Problemi con PostCSS/Tailwind

**Problema**: Errori di compilazione CSS durante la build.

**Soluzione**: Assicurati di avere un solo file di configurazione PostCSS:

- Mantieni `postcss.config.js`
- Rimuovi `postcss.config.mjs` se presente

### 10. Vantaggi Sistema Performance

- ✅ **Proattivo**: Identifica problemi prima degli utenti
- ✅ **Data-driven**: Decisioni basate su metriche reali
- ✅ **Automatizzato**: Monitoring continuo senza intervento
- ✅ **Scalabile**: Cresce con l'applicazione
- ✅ **Didattico**: Insegna ottimizzazione database

💡 **Suggerimento didattico**: Il monitoring delle performance è essenziale per applicazioni scalabili. pg_stat_statements + Grafana forniscono visibilità completa sulle query e permettono ottimizzazioni data-driven.

---

**Let's rebuild, together.**
