import { test, expect } from "@playwright/test";

/**
 * Test E2E "happy path" completo
 * Flusso: signin → crea annuncio (Project) → appare in lista → chat message → visibile realtime in seconda pagina
 */

const slug = "ua";

test("user can post announcement and chat realtime", async ({ page, context }) => {
  // — SIGNIN (magic link bypass) —
  console.log("🔐 Testing signin flow...");
  await page.goto("/signin?demo=true");              // route returns demo JWT cookie
  await expect(page).toHaveURL("/");                 // redirected home

  // — CREATE ANNOUNCEMENT —
  console.log("📝 Testing announcement creation...");
  await page.goto(`/${slug}/annunci/new`);
  
  // Compila il form per un Project
  await page.getByRole("textbox", { name: "<PERSON><PERSON>" }).fill("Test progetto E2E");
  await page.getByRole("textbox", { name: "Descrizione" }).fill("Descrizione lunga di prova per test end-to-end automatizzato con Playwright");
  await page.getByRole("textbox", { name: "Regione" }).fill("Kyiv");
  await page.getByRole("textbox", { name: "Budget min (€)" }).fill("1000");
  await page.getByRole("textbox", { name: "Budget max (€)" }).fill("5000");
  
  // Submit del form
  await page.getByRole("button", { name: "Pubblica annuncio" }).click();
  await expect(page).toHaveURL(`/${slug}/annunci`);

  // — VERIFY ANNOUNCEMENT IN LIST —
  console.log("📋 Testing announcement appears in list...");
  // annunci list contiene card
  const card = page.getByRole("link", { name: /Test progetto E2E/ });
  await expect(card).toBeVisible();

  // — CHAT REALTIME —
  console.log("💬 Testing chat functionality...");
  await card.click();
  
  // Trova il campo di input della chat
  const chatInput = page.getByPlaceholder("Scrivi un messaggio…").or(page.getByRole("textbox"));
  await chatInput.fill("Messaggio E2E 1");
  await chatInput.press("Enter");

  // Verifica che il messaggio appaia nella chat
  const sentMessage = page.getByText("Messaggio E2E 1");
  await expect(sentMessage).toBeVisible();

  // — REALTIME TEST CON SECONDA TAB —
  console.log("🔄 Testing realtime sync with second tab...");
  // Apri seconda tab per vedere realtime
  const page2 = await context.newPage();
  await page2.goto(page.url());
  
  // Verifica che il messaggio sia visibile anche nella seconda tab
  const bubble = page2.getByText("Messaggio E2E 1");
  await expect(bubble).toBeVisible({ timeout: 5000 });

  // — TEST BIDIREZIONALE —
  console.log("↔️ Testing bidirectional chat...");
  // Invia messaggio dalla seconda tab
  const chatInput2 = page2.getByPlaceholder("Scrivi un messaggio…").or(page2.getByRole("textbox"));
  await chatInput2.fill("Risposta dalla seconda tab");
  await chatInput2.press("Enter");

  // Verifica che il messaggio appaia in entrambe le tab
  const response1 = page.getByText("Risposta dalla seconda tab");
  const response2 = page2.getByText("Risposta dalla seconda tab");
  
  await expect(response1).toBeVisible({ timeout: 5000 });
  await expect(response2).toBeVisible({ timeout: 5000 });

  console.log("✅ Full flow test completed successfully!");
});
