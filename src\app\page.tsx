import CategoryMiniCard from '@/components/CategoryMiniCard';

export default function HomePage() {
  const categories = [
    'Motori',
    'Market',
    'Immobili',
    'Lavoro',
    'Servizi',
    'Elettronica',
    'Casa & Giardino',
    'Sport & Tempo Libero'
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-heading font-bold text-primary mb-4">Benvenuto su ReBuildLink</h1>
      <p className="text-lg text-gray-600 mb-8">
        La piattaforma per connettere le comunità attraverso annunci e servizi.
      </p>

      <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
        {categories.map((category) => (
          <CategoryMiniCard
            key={category}
            title={category}
          />
        ))}
      </div>
    </div>
  );
}
