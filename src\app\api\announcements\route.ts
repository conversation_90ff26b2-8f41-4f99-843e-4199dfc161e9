// auth middleware wired – creator_id da Supabase user
// Questo file gestisce GET e POST per gli annunci multi-tenant.
// POST ora richiede autenticazione JWT e usa l'id reale dell'utente Supabase come creator_id.

import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { validateAnnouncement } from '@/lib/validators';

import { requireAuth } from '@/middleware/auth';

export async function GET(req: NextRequest) {
  // Supporta sia header che query param per tenant
  const tenantSlug = req.nextUrl.searchParams.get('tenant');
  const tenantIdFromHeader = req.headers.get('x-tenant-id');

  // Parametri di filtro e paginazione
  const category = req.nextUrl.searchParams.get('cat') || '';
  const type = req.nextUrl.searchParams.get('type') || '';
  const minPrice = req.nextUrl.searchParams.get('min') || '';
  const maxPrice = req.nextUrl.searchParams.get('max') || '';
  const page = parseInt(req.nextUrl.searchParams.get('page') || '1');
  const pageSize = 12;

  let tenantId: string;

  if (tenantSlug) {
    // Mapping slug → tenant_id
    const TENANT_MAP: Record<string, string> = {
      'ua': '1',
      'it': '2',
    };
    tenantId = TENANT_MAP[tenantSlug] || '1'; // default a 'ua'
  } else if (tenantIdFromHeader) {
    tenantId = tenantIdFromHeader;
  } else {
    return NextResponse.json({ error: 'Tenant not specified (use ?tenant=ua or x-tenant-id header)' }, { status: 400 });
  }

  try {
    // Costruisci query dinamica con filtri
    let whereClause = 'WHERE tenant_id = $1';
    const params: any[] = [tenantId];
    let paramIndex = 2;

    if (category && category !== 'all') {
      whereClause += ` AND category = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }

    if (type && type !== 'all') {
      whereClause += ` AND announcement_type = $${paramIndex}`;
      params.push(type);
      paramIndex++;
    }

    if (minPrice) {
      whereClause += ` AND price >= $${paramIndex}`;
      params.push(parseInt(minPrice));
      paramIndex++;
    }

    if (maxPrice) {
      whereClause += ` AND price <= $${paramIndex}`;
      params.push(parseInt(maxPrice));
      paramIndex++;
    }

    // Query per il conteggio totale
    const countQuery = `SELECT COUNT(*) as total FROM announcements ${whereClause}`;
    const [countResult] = await query(countQuery, params);
    const total = parseInt(countResult.total);

    // Query per i dati paginati
    const offset = (page - 1) * pageSize;
    const dataQuery = `
      SELECT id,
             title,
             announcement_type AS type,
             category,
             region,
             location,
             description,
             price,
             created_at
        FROM announcements
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    params.push(pageSize, offset);

    const rows = await query(dataQuery, params);

    return NextResponse.json({
      items: rows,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    });

  } catch (error) {
    console.error('Database error:', error);

    // Fallback con dati mock se il DB non è disponibile
    const mockData = Array.from({ length: pageSize }, (_, i) => ({
      id: i + 1 + (page - 1) * pageSize,
      title: `Annuncio Mock ${i + 1 + (page - 1) * pageSize}`,
      type: i % 2 === 0 ? 'project' : 'job',
      category: ['motori', 'market', 'immobili', 'lavoro'][i % 4],
      region: 'Regione Mock',
      location: 'Città Mock',
      description: 'Descrizione di esempio per questo annuncio mock.',
      price: Math.floor(Math.random() * 10000) + 1000,
      created_at: new Date().toISOString()
    }));

    // Applica filtri ai dati mock
    let filteredData = mockData;
    if (category && category !== 'all') {
      filteredData = filteredData.filter(item => item.category === category);
    }
    if (type && type !== 'all') {
      filteredData = filteredData.filter(item => item.type === type);
    }

    const mockTotal = 48; // Simula 4 pagine di dati

    return NextResponse.json({
      items: filteredData,
      total: mockTotal,
      page,
      pageSize,
      totalPages: Math.ceil(mockTotal / pageSize)
    });
  }
}

export async function POST(req: NextRequest) {
  // Autenticazione JWT obbligatoria
  const auth = await requireAuth(req);
  if ('error' in auth) return auth.error;
  const creatorId = auth.user.id;

  const tenantId = req.headers.get('x-tenant-id');
  if (!tenantId) return NextResponse.json({ error: 'Tenant not found' }, { status: 400 });

  let body: unknown;
  try {
    body = await req.json();
  } catch {
    return NextResponse.json({ error: 'JSON malformato' }, { status: 400 });
  }

  const result = validateAnnouncement(body);
  if (!result.success) {
    return NextResponse.json({ error: 'Validazione fallita', details: result.error.errors }, { status: 422 });
  }

  const { title, description, type, region, location } = result.data;
  try {
    const insertQuery = `
      INSERT INTO announcements (tenant_id, creator_id, announcement_type, title, description, region, location)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, title, announcement_type AS type, region, location
    `;
    const values = [tenantId, creatorId, type, title, description ?? null, region ?? null, location ?? null];
    const [row] = await query(insertQuery, values);
    return NextResponse.json(row, { status: 201 });
  } catch {
    return NextResponse.json({ error: 'Errore inserimento annuncio' }, { status: 500 });
  }
}
