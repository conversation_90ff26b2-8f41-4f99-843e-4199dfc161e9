// 📄 src/app/[slug]/annunci/new/page.tsx
// Form creazione annuncio v2 con react-hook-form + Zod validation
// Multi-tenant (Project/Job) con UX moderna e validazione robusta

"use client";
import { useRouter, useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

// Schema Zod per validazione
const schema = z.object({
  type: z.enum(["Project", "Job"]),
  title: z.string().min(5, "Almeno 5 caratteri"),
  description: z.string().min(20, "Almeno 20 caratteri"),
  region: z.string().optional(),
  budgetMin: z.coerce.number().positive().optional(),
  budgetMax: z.coerce.number().positive().optional(),
  location: z.string().optional(),
  salary: z.string().optional(),
  availability: z.string().optional(),
}).refine(data => (
  data.type === "Project"
    ? data.region && data.budgetMin && data.budgetMax
    : data.location && data.salary && data.availability
), { message: "Compila tutti i campi del tipo selezionato" });

type FormData = z.infer<typeof schema>;

export default function NewAnnouncementPage() {
  const router = useRouter();
  const params = useParams();
  const slug = params.slug as string;

  // React Hook Form setup
  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: { type: "Project" },
  });

  // Submit handler
  async function onSubmit(values: FormData) {
    try {
      const res = await fetch("/api/announcements", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-tenant-id": slug
        },
        body: JSON.stringify(values),
      });

      if (res.ok) {
        router.push(`/${slug}/annunci`);
      } else {
        form.setError("title", { message: "Errore salvataggio" });
      }
    } catch (error) {
      form.setError("title", { message: "Errore di rete" });
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <header className="mb-8">
          <h1 className="text-3xl font-heading font-bold text-primary-700 mb-2">
            Nuovo Annuncio - {slug.toUpperCase()}
          </h1>
          <p className="text-gray-600">
            Crea un nuovo annuncio per la tua comunità
          </p>
        </header>

        <div className="bg-white rounded-xl shadow p-6">
          <Tabs
            value={form.watch("type")}
            onValueChange={(val) => form.setValue("type", val as "Project" | "Job")}
            className="w-full"
          >
            <TabsList className="mb-6">
              <TabsTrigger value="Project">Project</TabsTrigger>
              <TabsTrigger value="Job">Job</TabsTrigger>
            </TabsList>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                {/* Campi comuni */}
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Titolo *</FormLabel>
                      <Input {...field} placeholder="Inserisci il titolo dell'annuncio" />
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrizione *</FormLabel>
                      <Textarea
                        rows={4}
                        {...field}
                        placeholder="Descrivi dettagliatamente l'annuncio..."
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Campi specifici per Project */}
                <TabsContent value="Project" className="space-y-4">
                  <FormField
                    name="region"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Regione *</FormLabel>
                        <Input {...field} placeholder="Es. Kyiv, Lviv, Lazio..." />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex gap-4">
                    <FormField
                      name="budgetMin"
                      control={form.control}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Budget min (€) *</FormLabel>
                          <Input type="number" {...field} placeholder="1000" />
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      name="budgetMax"
                      control={form.control}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Budget max (€) *</FormLabel>
                          <Input type="number" {...field} placeholder="5000" />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>

                {/* Campi specifici per Job */}
                <TabsContent value="Job" className="space-y-4">
                  <FormField
                    name="location"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Località *</FormLabel>
                        <Input {...field} placeholder="Es. Milano, Roma, Kyiv..." />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    name="salary"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Retribuzione *</FormLabel>
                        <Input {...field} placeholder="Es. €1500-2000/mese" />
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    name="availability"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Disponibilità *</FormLabel>
                        <Input
                          {...field}
                          placeholder="Full-time / Part-time / Temporaneo"
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <Button
                  type="submit"
                  className="bg-primary-700 hover:bg-primary-500 w-full"
                  disabled={form.formState.isSubmitting}
                >
                  {form.formState.isSubmitting ? "Salvataggio…" : "Pubblica annuncio"}
                </Button>
              </form>
            </Form>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
