
import type { Metadata } from 'next'
import './globals.css'
import { LocaleProvider } from "@/components/LocaleContext";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export const metadata: Metadata = {
  title: 'Rebuild Link',
  description: 'Piattaforma per annunci e collegamenti',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="it">
      <body className="font-inter antialiased">
        <LocaleProvider>
          <Navbar />
          <main className="min-h-screen">
            {children}
          </main>
          <Footer />
        </LocaleProvider>
      </body>
    </html>
  )
}
