"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Search, Car, ShoppingBag, Home, Briefcase } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

/**
 * Barra di ricerca con tre pill: query, categoria, location
 * Submit naviga a /search con parametri query
 */
export default function SearchBar() {
  const router = useRouter();
  const [query, setQuery] = useState("");
  const [category, setCategory] = useState("all");
  const [location, setLocation] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams();
    if (query) params.set('query', query);
    if (category && category !== 'all') params.set('cat', category);
    if (location) params.set('loc', location);
    
    router.push(`/search?${params.toString()}`);
  };

  const getCategoryIcon = (cat: string) => {
    switch (cat) {
      case 'motori': return <Car className="w-4 h-4" />;
      case 'market': return <ShoppingBag className="w-4 h-4" />;
      case 'immobili': return <Home className="w-4 h-4" />;
      case 'lavoro': return <Briefcase className="w-4 h-4" />;
      default: return null;
    }
  };

  const getCategoryColor = (cat: string) => {
    switch (cat) {
      case 'motori': return 'bg-secondary-blue/20 text-secondary-blue';
      case 'market': return 'bg-secondary-green/20 text-secondary-green';
      case 'immobili': return 'bg-secondary-purple/20 text-secondary-purple';
      case 'lavoro': return 'bg-secondary-yellow/20 text-secondary-yellow';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="flex w-full rounded-full shadow-sm overflow-hidden border bg-white">
        {/* Query Input */}
        <input
          type="text"
          placeholder="Cosa cerchi?"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="flex-1 px-4 py-3 border-none outline-none text-sm"
        />
        
        {/* Category Select */}
        <div className="border-l px-2 py-2">
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger className="border-none shadow-none">
              <div className={`font-medium rounded-full px-3 py-1 text-sm inline-flex gap-1 items-center ${getCategoryColor(category)}`}>
                {getCategoryIcon(category)}
                <SelectValue placeholder="Categoria" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tutte</SelectItem>
              <SelectItem value="motori">Motori</SelectItem>
              <SelectItem value="market">Market</SelectItem>
              <SelectItem value="immobili">Immobili</SelectItem>
              <SelectItem value="lavoro">Lavoro</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Location Input */}
        <input
          type="text"
          placeholder="Dove?"
          value={location}
          onChange={(e) => setLocation(e.target.value)}
          className="w-32 px-4 py-3 border-none border-l outline-none text-sm"
        />
        
        {/* Search Button */}
        <Button 
          type="submit" 
          className="bg-primary text-white rounded-none rounded-r-full px-6 hover:bg-primary/90"
        >
          <Search className="w-4 h-4" />
        </Button>
      </div>
    </form>
  );
}
